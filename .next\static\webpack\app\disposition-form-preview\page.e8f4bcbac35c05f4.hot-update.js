"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/disposition-form-preview/page",{

/***/ "(app-pages-browser)/./src/app/disposition-form-preview/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/disposition-form-preview/page.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DispositionFormPreviewPage; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_DispositionFormPreview__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/DispositionFormPreview */ \"(app-pages-browser)/./src/components/DispositionFormPreview.tsx\");\n/* harmony import */ var _utils_apiUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/apiUtils */ \"(app-pages-browser)/./src/utils/apiUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction DispositionFormPreviewPage() {\n    _s();\n    var searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    var documentId = searchParams.get(\"id\") || \"\";\n    var documentTitle = searchParams.get(\"title\") || \"\";\n    var isBlank = searchParams.get(\"blank\") === \"true\";\n    var journeyDataParam = searchParams.get(\"journeyData\");\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]), 2), journey = _useState[0], setJourney = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true), 2), loading = _useState1[0], setLoading = _useState1[1];\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"), 2), trackingNumber = _useState2[0], setTrackingNumber = _useState2[1];\n    // Debug logging for URL parameters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        console.log(\"Disposition Form Preview loaded with params:\", {\n            documentId: documentId,\n            documentTitle: documentTitle,\n            isBlank: isBlank,\n            hasJourneyData: !!journeyDataParam,\n            journeyDataLength: (journeyDataParam === null || journeyDataParam === void 0 ? void 0 : journeyDataParam.length) || 0,\n            fullURL: window.location.href\n        });\n    }, [\n        documentId,\n        documentTitle,\n        isBlank,\n        journeyDataParam\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        // Always fetch document data to get tracking number (unless blank)\n        if (!isBlank && documentId) {\n            fetchDocumentData();\n        }\n        // Check if journey data is provided in URL parameters\n        if (journeyDataParam) {\n            try {\n                var parsedJourney = JSON.parse(decodeURIComponent(journeyDataParam));\n                console.log(\"Using journey data from URL parameters:\", parsedJourney.length, \"entries\");\n                setJourney(parsedJourney);\n                if (isBlank) setLoading(false); // If blank, we're done\n                return;\n            } catch (error) {\n                console.error(\"Error parsing journey data from URL:\", error);\n            // Fall through to API fetch\n            }\n        }\n        // Only fetch journey data if not a blank form and no journey data provided\n        if (!isBlank && documentId && !journeyDataParam) {\n            fetchJourneyData();\n        } else if (isBlank) {\n            setLoading(false);\n        }\n    }, [\n        documentId,\n        isBlank,\n        journeyDataParam\n    ]);\n    var fetchJourneyData = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_6__._)(function() {\n            var encodedDocumentId, apiUrl, response, data, text, parseError, allEntries, sortedEntries, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_7__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        _state.trys.push([\n                            0,\n                            9,\n                            11,\n                            12\n                        ]);\n                        // Use the document-chain API to get the complete journey\n                        encodedDocumentId = encodeURIComponent(documentId);\n                        apiUrl = (0,_utils_apiUtils__WEBPACK_IMPORTED_MODULE_4__.getApiUrl)(\"/api/documents/\".concat(encodedDocumentId, \"/document-chain\"));\n                        console.log(\"Fetching complete document chain from:\", apiUrl);\n                        return [\n                            4,\n                            fetch(apiUrl, {\n                                method: \"GET\",\n                                headers: _utils_apiUtils__WEBPACK_IMPORTED_MODULE_4__.defaultHeaders\n                            })\n                        ];\n                    case 1:\n                        response = _state.sent();\n                        if (!response.ok) {\n                            console.error(\"Failed to fetch document chain: \".concat(response.status, \" \").concat(response.statusText));\n                            setJourney([]);\n                            return [\n                                2\n                            ];\n                        }\n                        _state.label = 2;\n                    case 2:\n                        _state.trys.push([\n                            2,\n                            4,\n                            ,\n                            5\n                        ]);\n                        return [\n                            4,\n                            response.text()\n                        ];\n                    case 3:\n                        text = _state.sent();\n                        console.log(\"Raw document chain response:\", text.substring(0, 100) + (text.length > 100 ? \"...\" : \"\"));\n                        data = JSON.parse(text);\n                        return [\n                            3,\n                            5\n                        ];\n                    case 4:\n                        parseError = _state.sent();\n                        console.error(\"Error parsing document chain data:\", parseError);\n                        setJourney([]);\n                        return [\n                            2\n                        ];\n                    case 5:\n                        if (!(data.success && data.documentChain && Array.isArray(data.documentChain))) return [\n                            3,\n                            6\n                        ];\n                        console.log(\"Document chain received:\", data.documentChain.length, \"documents\");\n                        console.log(\"Document chain data:\", JSON.stringify(data.documentChain, null, 2));\n                        // Combine all journey entries from all documents in the chain\n                        allEntries = [];\n                        data.documentChain.forEach(function(doc, index) {\n                            var _doc_journey;\n                            console.log(\"Processing document \".concat(index + 1, \":\"), {\n                                id: doc._id,\n                                title: doc.title,\n                                journeyLength: ((_doc_journey = doc.journey) === null || _doc_journey === void 0 ? void 0 : _doc_journey.length) || 0,\n                                journey: doc.journey\n                            });\n                            if (doc.journey && doc.journey.length > 0) {\n                                var _allEntries;\n                                // Add document title to each journey entry for better context\n                                var journeyWithDocTitle = doc.journey.map(function(entry) {\n                                    return (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_8__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_9__._)({}, entry), {\n                                        documentTitle: doc.title\n                                    });\n                                });\n                                (_allEntries = allEntries).push.apply(_allEntries, (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_10__._)(journeyWithDocTitle));\n                            } else {\n                                console.warn(\"Document \".concat(doc._id, \" has no journey entries\"));\n                            }\n                        });\n                        // Sort entries by timestamp\n                        sortedEntries = allEntries.sort(function(a, b) {\n                            var dateA = new Date(a.timestamp);\n                            var dateB = new Date(b.timestamp);\n                            return dateA.getTime() - dateB.getTime();\n                        });\n                        console.log(\"Combined journey entries:\", sortedEntries.length);\n                        console.log(\"Final journey data:\", JSON.stringify(sortedEntries, null, 2));\n                        setJourney(sortedEntries);\n                        return [\n                            3,\n                            8\n                        ];\n                    case 6:\n                        console.error(\"Invalid document chain data:\", data);\n                        // Fallback to the original journey API if document-chain fails\n                        console.log(\"Falling back to journey API\");\n                        return [\n                            4,\n                            fetchFallbackJourneyData()\n                        ];\n                    case 7:\n                        _state.sent();\n                        _state.label = 8;\n                    case 8:\n                        return [\n                            3,\n                            12\n                        ];\n                    case 9:\n                        error = _state.sent();\n                        console.error(\"Error fetching document chain:\", error);\n                        // Fallback to the original journey API if document-chain fails\n                        console.log(\"Falling back to journey API due to error\");\n                        return [\n                            4,\n                            fetchFallbackJourneyData()\n                        ];\n                    case 10:\n                        _state.sent();\n                        return [\n                            3,\n                            12\n                        ];\n                    case 11:\n                        setLoading(false);\n                        return [\n                            7\n                        ];\n                    case 12:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function fetchJourneyData() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    // Fallback to the original journey API if document-chain fails\n    var fetchFallbackJourneyData = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_6__._)(function() {\n            var encodedDocumentId, apiUrl, response, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_7__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        _state.trys.push([\n                            0,\n                            3,\n                            ,\n                            4\n                        ]);\n                        encodedDocumentId = encodeURIComponent(documentId);\n                        apiUrl = (0,_utils_apiUtils__WEBPACK_IMPORTED_MODULE_4__.getApiUrl)(\"/api/documents/\".concat(encodedDocumentId, \"/journey\"));\n                        console.log(\"Fetching fallback journey data from:\", apiUrl);\n                        return [\n                            4,\n                            fetch(apiUrl, {\n                                method: \"GET\",\n                                headers: _utils_apiUtils__WEBPACK_IMPORTED_MODULE_4__.defaultHeaders\n                            })\n                        ];\n                    case 1:\n                        response = _state.sent();\n                        if (!response.ok) {\n                            console.error(\"Failed to fetch fallback journey data: \".concat(response.status, \" \").concat(response.statusText));\n                            setJourney([]);\n                            return [\n                                2\n                            ];\n                        }\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 2:\n                        data = _state.sent();\n                        if (Array.isArray(data)) {\n                            console.log(\"Fallback journey data received:\", data.length);\n                            console.log(\"Fallback journey data:\", JSON.stringify(data, null, 2));\n                            setJourney(data);\n                        } else {\n                            console.error(\"Fallback journey data is not an array:\", data);\n                            console.log(\"Fallback response data:\", JSON.stringify(data, null, 2));\n                            setJourney([]);\n                        }\n                        return [\n                            3,\n                            4\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error fetching fallback journey data:\", error);\n                        setJourney([]);\n                        return [\n                            3,\n                            4\n                        ];\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function fetchFallbackJourneyData() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\app\\\\disposition-form-preview\\\\page.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-700 dark:text-gray-300\",\n                        children: \"Loading disposition form...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\app\\\\disposition-form-preview\\\\page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\app\\\\disposition-form-preview\\\\page.tsx\",\n                lineNumber: 184,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\app\\\\disposition-form-preview\\\\page.tsx\",\n            lineNumber: 183,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DispositionFormPreview__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        documentId: documentId,\n        documentTitle: documentTitle,\n        journey: journey,\n        isBlank: isBlank\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\app\\\\disposition-form-preview\\\\page.tsx\",\n        lineNumber: 193,\n        columnNumber: 5\n    }, this);\n}\n_s(DispositionFormPreviewPage, \"FqFEstiTYuVjH9TFK/IoOwhbvqA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = DispositionFormPreviewPage;\nvar _c;\n$RefreshReg$(_c, \"DispositionFormPreviewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/disposition-form-preview/page.tsx\n"));

/***/ })

});