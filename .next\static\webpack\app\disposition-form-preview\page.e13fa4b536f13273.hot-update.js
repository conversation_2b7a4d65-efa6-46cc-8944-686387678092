"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/disposition-form-preview/page",{

/***/ "(app-pages-browser)/./src/app/disposition-form-preview/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/disposition-form-preview/page.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DispositionFormPreviewPage; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_DispositionFormPreview__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/DispositionFormPreview */ \"(app-pages-browser)/./src/components/DispositionFormPreview.tsx\");\n/* harmony import */ var _utils_apiUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/apiUtils */ \"(app-pages-browser)/./src/utils/apiUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction DispositionFormPreviewPage() {\n    _s();\n    var searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    var documentId = searchParams.get(\"id\") || \"\";\n    var documentTitle = searchParams.get(\"title\") || \"\";\n    var isBlank = searchParams.get(\"blank\") === \"true\";\n    var journeyDataParam = searchParams.get(\"journeyData\");\n    var trackingNumberParam = searchParams.get(\"trackingNumber\");\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]), 2), journey = _useState[0], setJourney = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true), 2), loading = _useState1[0], setLoading = _useState1[1];\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"), 2), trackingNumber = _useState2[0], setTrackingNumber = _useState2[1];\n    // Debug logging for URL parameters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        console.log(\"Disposition Form Preview loaded with params:\", {\n            documentId: documentId,\n            documentTitle: documentTitle,\n            isBlank: isBlank,\n            hasJourneyData: !!journeyDataParam,\n            journeyDataLength: (journeyDataParam === null || journeyDataParam === void 0 ? void 0 : journeyDataParam.length) || 0,\n            trackingNumberParam: trackingNumberParam,\n            fullURL: window.location.href\n        });\n    }, [\n        documentId,\n        documentTitle,\n        isBlank,\n        journeyDataParam,\n        trackingNumberParam\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        // Check if tracking number is provided in URL parameters\n        if (trackingNumberParam) {\n            console.log(\"Using tracking number from URL parameters:\", trackingNumberParam);\n            setTrackingNumber(trackingNumberParam);\n        }\n        // Always fetch document data to get tracking number (unless blank or already provided)\n        if (!isBlank && documentId && !trackingNumberParam) {\n            fetchDocumentData();\n        }\n        // Check if journey data is provided in URL parameters\n        if (journeyDataParam) {\n            try {\n                var parsedJourney = JSON.parse(decodeURIComponent(journeyDataParam));\n                console.log(\"Using journey data from URL parameters:\", parsedJourney.length, \"entries\");\n                setJourney(parsedJourney);\n                if (isBlank || trackingNumberParam) setLoading(false); // If blank or tracking number provided, we're done\n                return;\n            } catch (error) {\n                console.error(\"Error parsing journey data from URL:\", error);\n            // Fall through to API fetch\n            }\n        }\n        // Only fetch journey data if not a blank form and no journey data provided\n        if (!isBlank && documentId && !journeyDataParam) {\n            fetchJourneyData();\n        } else if (isBlank) {\n            setLoading(false);\n        }\n    }, [\n        documentId,\n        isBlank,\n        journeyDataParam,\n        trackingNumberParam\n    ]);\n    // Fetch document data to get the tracking number\n    var fetchDocumentData = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_6__._)(function() {\n            var encodedDocumentId, apiUrl, response, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_7__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        _state.trys.push([\n                            0,\n                            3,\n                            4,\n                            5\n                        ]);\n                        encodedDocumentId = encodeURIComponent(documentId);\n                        apiUrl = (0,_utils_apiUtils__WEBPACK_IMPORTED_MODULE_4__.getApiUrl)(\"/api/documents/\".concat(encodedDocumentId));\n                        console.log(\"Fetching document data from:\", apiUrl);\n                        return [\n                            4,\n                            fetch(apiUrl, {\n                                method: \"GET\",\n                                headers: _utils_apiUtils__WEBPACK_IMPORTED_MODULE_4__.defaultHeaders\n                            })\n                        ];\n                    case 1:\n                        response = _state.sent();\n                        if (!response.ok) {\n                            console.error(\"Failed to fetch document data: \".concat(response.status, \" \").concat(response.statusText));\n                            return [\n                                2\n                            ];\n                        }\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 2:\n                        data = _state.sent();\n                        console.log(\"Document data received:\", data);\n                        if (data && data.trackingNumber) {\n                            console.log(\"Setting tracking number:\", data.trackingNumber);\n                            setTrackingNumber(data.trackingNumber);\n                        } else {\n                            console.warn(\"No tracking number found in document data\");\n                        }\n                        return [\n                            3,\n                            5\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error fetching document data:\", error);\n                        return [\n                            3,\n                            5\n                        ];\n                    case 4:\n                        // Don't set loading to false here, wait for journey data\n                        if (journeyDataParam || isBlank) {\n                            setLoading(false);\n                        }\n                        return [\n                            7\n                        ];\n                    case 5:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function fetchDocumentData() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    var fetchJourneyData = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_6__._)(function() {\n            var encodedDocumentId, apiUrl, response, data, text, parseError, allEntries, sortedEntries, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_7__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        _state.trys.push([\n                            0,\n                            9,\n                            11,\n                            12\n                        ]);\n                        // Use the document-chain API to get the complete journey\n                        encodedDocumentId = encodeURIComponent(documentId);\n                        apiUrl = (0,_utils_apiUtils__WEBPACK_IMPORTED_MODULE_4__.getApiUrl)(\"/api/documents/\".concat(encodedDocumentId, \"/document-chain\"));\n                        console.log(\"Fetching complete document chain from:\", apiUrl);\n                        return [\n                            4,\n                            fetch(apiUrl, {\n                                method: \"GET\",\n                                headers: _utils_apiUtils__WEBPACK_IMPORTED_MODULE_4__.defaultHeaders\n                            })\n                        ];\n                    case 1:\n                        response = _state.sent();\n                        if (!response.ok) {\n                            console.error(\"Failed to fetch document chain: \".concat(response.status, \" \").concat(response.statusText));\n                            setJourney([]);\n                            return [\n                                2\n                            ];\n                        }\n                        _state.label = 2;\n                    case 2:\n                        _state.trys.push([\n                            2,\n                            4,\n                            ,\n                            5\n                        ]);\n                        return [\n                            4,\n                            response.text()\n                        ];\n                    case 3:\n                        text = _state.sent();\n                        console.log(\"Raw document chain response:\", text.substring(0, 100) + (text.length > 100 ? \"...\" : \"\"));\n                        data = JSON.parse(text);\n                        return [\n                            3,\n                            5\n                        ];\n                    case 4:\n                        parseError = _state.sent();\n                        console.error(\"Error parsing document chain data:\", parseError);\n                        setJourney([]);\n                        return [\n                            2\n                        ];\n                    case 5:\n                        if (!(data.success && data.documentChain && Array.isArray(data.documentChain))) return [\n                            3,\n                            6\n                        ];\n                        console.log(\"Document chain received:\", data.documentChain.length, \"documents\");\n                        console.log(\"Document chain data:\", JSON.stringify(data.documentChain, null, 2));\n                        // Combine all journey entries from all documents in the chain\n                        allEntries = [];\n                        data.documentChain.forEach(function(doc, index) {\n                            var _doc_journey;\n                            console.log(\"Processing document \".concat(index + 1, \":\"), {\n                                id: doc._id,\n                                title: doc.title,\n                                journeyLength: ((_doc_journey = doc.journey) === null || _doc_journey === void 0 ? void 0 : _doc_journey.length) || 0,\n                                journey: doc.journey\n                            });\n                            if (doc.journey && doc.journey.length > 0) {\n                                var _allEntries;\n                                // Add document title to each journey entry for better context\n                                var journeyWithDocTitle = doc.journey.map(function(entry) {\n                                    return (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_8__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_9__._)({}, entry), {\n                                        documentTitle: doc.title\n                                    });\n                                });\n                                (_allEntries = allEntries).push.apply(_allEntries, (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_10__._)(journeyWithDocTitle));\n                            } else {\n                                console.warn(\"Document \".concat(doc._id, \" has no journey entries\"));\n                            }\n                        });\n                        // Sort entries by timestamp\n                        sortedEntries = allEntries.sort(function(a, b) {\n                            var dateA = new Date(a.timestamp);\n                            var dateB = new Date(b.timestamp);\n                            return dateA.getTime() - dateB.getTime();\n                        });\n                        console.log(\"Combined journey entries:\", sortedEntries.length);\n                        console.log(\"Final journey data:\", JSON.stringify(sortedEntries, null, 2));\n                        setJourney(sortedEntries);\n                        return [\n                            3,\n                            8\n                        ];\n                    case 6:\n                        console.error(\"Invalid document chain data:\", data);\n                        // Fallback to the original journey API if document-chain fails\n                        console.log(\"Falling back to journey API\");\n                        return [\n                            4,\n                            fetchFallbackJourneyData()\n                        ];\n                    case 7:\n                        _state.sent();\n                        _state.label = 8;\n                    case 8:\n                        return [\n                            3,\n                            12\n                        ];\n                    case 9:\n                        error = _state.sent();\n                        console.error(\"Error fetching document chain:\", error);\n                        // Fallback to the original journey API if document-chain fails\n                        console.log(\"Falling back to journey API due to error\");\n                        return [\n                            4,\n                            fetchFallbackJourneyData()\n                        ];\n                    case 10:\n                        _state.sent();\n                        return [\n                            3,\n                            12\n                        ];\n                    case 11:\n                        setLoading(false);\n                        return [\n                            7\n                        ];\n                    case 12:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function fetchJourneyData() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    // Fallback to the original journey API if document-chain fails\n    var fetchFallbackJourneyData = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_6__._)(function() {\n            var encodedDocumentId, apiUrl, response, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_7__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        _state.trys.push([\n                            0,\n                            3,\n                            ,\n                            4\n                        ]);\n                        encodedDocumentId = encodeURIComponent(documentId);\n                        apiUrl = (0,_utils_apiUtils__WEBPACK_IMPORTED_MODULE_4__.getApiUrl)(\"/api/documents/\".concat(encodedDocumentId, \"/journey\"));\n                        console.log(\"Fetching fallback journey data from:\", apiUrl);\n                        return [\n                            4,\n                            fetch(apiUrl, {\n                                method: \"GET\",\n                                headers: _utils_apiUtils__WEBPACK_IMPORTED_MODULE_4__.defaultHeaders\n                            })\n                        ];\n                    case 1:\n                        response = _state.sent();\n                        if (!response.ok) {\n                            console.error(\"Failed to fetch fallback journey data: \".concat(response.status, \" \").concat(response.statusText));\n                            setJourney([]);\n                            return [\n                                2\n                            ];\n                        }\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 2:\n                        data = _state.sent();\n                        if (Array.isArray(data)) {\n                            console.log(\"Fallback journey data received:\", data.length);\n                            console.log(\"Fallback journey data:\", JSON.stringify(data, null, 2));\n                            setJourney(data);\n                        } else {\n                            console.error(\"Fallback journey data is not an array:\", data);\n                            console.log(\"Fallback response data:\", JSON.stringify(data, null, 2));\n                            setJourney([]);\n                        }\n                        return [\n                            3,\n                            4\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error fetching fallback journey data:\", error);\n                        setJourney([]);\n                        return [\n                            3,\n                            4\n                        ];\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function fetchFallbackJourneyData() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\app\\\\disposition-form-preview\\\\page.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-700 dark:text-gray-300\",\n                        children: \"Loading disposition form...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\app\\\\disposition-form-preview\\\\page.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\app\\\\disposition-form-preview\\\\page.tsx\",\n                lineNumber: 228,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\app\\\\disposition-form-preview\\\\page.tsx\",\n            lineNumber: 227,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DispositionFormPreview__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        documentId: documentId,\n        documentTitle: documentTitle,\n        journey: journey,\n        isBlank: isBlank,\n        trackingNumber: trackingNumber\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\app\\\\disposition-form-preview\\\\page.tsx\",\n        lineNumber: 237,\n        columnNumber: 5\n    }, this);\n}\n_s(DispositionFormPreviewPage, \"FqFEstiTYuVjH9TFK/IoOwhbvqA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = DispositionFormPreviewPage;\nvar _c;\n$RefreshReg$(_c, \"DispositionFormPreviewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/disposition-form-preview/page.tsx\n"));

/***/ })

});