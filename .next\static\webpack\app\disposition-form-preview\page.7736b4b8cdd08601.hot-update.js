"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/disposition-form-preview/page",{

/***/ "(app-pages-browser)/./src/app/disposition-form-preview/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/disposition-form-preview/page.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DispositionFormPreviewPage; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_DispositionFormPreview__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/DispositionFormPreview */ \"(app-pages-browser)/./src/components/DispositionFormPreview.tsx\");\n/* harmony import */ var _utils_apiUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/apiUtils */ \"(app-pages-browser)/./src/utils/apiUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction DispositionFormPreviewPage() {\n    _s();\n    var searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    var documentId = searchParams.get(\"id\") || \"\";\n    var documentTitle = searchParams.get(\"title\") || \"\";\n    var isBlank = searchParams.get(\"blank\") === \"true\";\n    var journeyDataParam = searchParams.get(\"journeyData\");\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]), 2), journey = _useState[0], setJourney = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true), 2), loading = _useState1[0], setLoading = _useState1[1];\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"), 2), trackingNumber = _useState2[0], setTrackingNumber = _useState2[1];\n    // Debug logging for URL parameters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        console.log(\"Disposition Form Preview loaded with params:\", {\n            documentId: documentId,\n            documentTitle: documentTitle,\n            isBlank: isBlank,\n            hasJourneyData: !!journeyDataParam,\n            journeyDataLength: (journeyDataParam === null || journeyDataParam === void 0 ? void 0 : journeyDataParam.length) || 0,\n            fullURL: window.location.href\n        });\n    }, [\n        documentId,\n        documentTitle,\n        isBlank,\n        journeyDataParam\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        // Always fetch document data to get tracking number (unless blank)\n        if (!isBlank && documentId) {\n            fetchDocumentData();\n        }\n        // Check if journey data is provided in URL parameters\n        if (journeyDataParam) {\n            try {\n                var parsedJourney = JSON.parse(decodeURIComponent(journeyDataParam));\n                console.log(\"Using journey data from URL parameters:\", parsedJourney.length, \"entries\");\n                setJourney(parsedJourney);\n                if (isBlank) setLoading(false); // If blank, we're done\n                return;\n            } catch (error) {\n                console.error(\"Error parsing journey data from URL:\", error);\n            // Fall through to API fetch\n            }\n        }\n        // Only fetch journey data if not a blank form and no journey data provided\n        if (!isBlank && documentId && !journeyDataParam) {\n            fetchJourneyData();\n        } else if (isBlank) {\n            setLoading(false);\n        }\n    }, [\n        documentId,\n        isBlank,\n        journeyDataParam\n    ]);\n    // Fetch document data to get the tracking number\n    var fetchDocumentData = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_6__._)(function() {\n            var encodedDocumentId, apiUrl, response, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_7__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        _state.trys.push([\n                            0,\n                            3,\n                            4,\n                            5\n                        ]);\n                        encodedDocumentId = encodeURIComponent(documentId);\n                        apiUrl = (0,_utils_apiUtils__WEBPACK_IMPORTED_MODULE_4__.getApiUrl)(\"/api/documents/\".concat(encodedDocumentId));\n                        console.log(\"Fetching document data from:\", apiUrl);\n                        return [\n                            4,\n                            fetch(apiUrl, {\n                                method: \"GET\",\n                                headers: _utils_apiUtils__WEBPACK_IMPORTED_MODULE_4__.defaultHeaders\n                            })\n                        ];\n                    case 1:\n                        response = _state.sent();\n                        if (!response.ok) {\n                            console.error(\"Failed to fetch document data: \".concat(response.status, \" \").concat(response.statusText));\n                            return [\n                                2\n                            ];\n                        }\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 2:\n                        data = _state.sent();\n                        console.log(\"Document data received:\", data);\n                        if (data && data.trackingNumber) {\n                            console.log(\"Setting tracking number:\", data.trackingNumber);\n                            setTrackingNumber(data.trackingNumber);\n                        } else {\n                            console.warn(\"No tracking number found in document data\");\n                        }\n                        return [\n                            3,\n                            5\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error fetching document data:\", error);\n                        return [\n                            3,\n                            5\n                        ];\n                    case 4:\n                        // Don't set loading to false here, wait for journey data\n                        if (journeyDataParam || isBlank) {\n                            setLoading(false);\n                        }\n                        return [\n                            7\n                        ];\n                    case 5:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function fetchDocumentData() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    var fetchJourneyData = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_6__._)(function() {\n            var encodedDocumentId, apiUrl, response, data, text, parseError, allEntries, sortedEntries, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_7__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        _state.trys.push([\n                            0,\n                            9,\n                            11,\n                            12\n                        ]);\n                        // Use the document-chain API to get the complete journey\n                        encodedDocumentId = encodeURIComponent(documentId);\n                        apiUrl = (0,_utils_apiUtils__WEBPACK_IMPORTED_MODULE_4__.getApiUrl)(\"/api/documents/\".concat(encodedDocumentId, \"/document-chain\"));\n                        console.log(\"Fetching complete document chain from:\", apiUrl);\n                        return [\n                            4,\n                            fetch(apiUrl, {\n                                method: \"GET\",\n                                headers: _utils_apiUtils__WEBPACK_IMPORTED_MODULE_4__.defaultHeaders\n                            })\n                        ];\n                    case 1:\n                        response = _state.sent();\n                        if (!response.ok) {\n                            console.error(\"Failed to fetch document chain: \".concat(response.status, \" \").concat(response.statusText));\n                            setJourney([]);\n                            return [\n                                2\n                            ];\n                        }\n                        _state.label = 2;\n                    case 2:\n                        _state.trys.push([\n                            2,\n                            4,\n                            ,\n                            5\n                        ]);\n                        return [\n                            4,\n                            response.text()\n                        ];\n                    case 3:\n                        text = _state.sent();\n                        console.log(\"Raw document chain response:\", text.substring(0, 100) + (text.length > 100 ? \"...\" : \"\"));\n                        data = JSON.parse(text);\n                        return [\n                            3,\n                            5\n                        ];\n                    case 4:\n                        parseError = _state.sent();\n                        console.error(\"Error parsing document chain data:\", parseError);\n                        setJourney([]);\n                        return [\n                            2\n                        ];\n                    case 5:\n                        if (!(data.success && data.documentChain && Array.isArray(data.documentChain))) return [\n                            3,\n                            6\n                        ];\n                        console.log(\"Document chain received:\", data.documentChain.length, \"documents\");\n                        console.log(\"Document chain data:\", JSON.stringify(data.documentChain, null, 2));\n                        // Combine all journey entries from all documents in the chain\n                        allEntries = [];\n                        data.documentChain.forEach(function(doc, index) {\n                            var _doc_journey;\n                            console.log(\"Processing document \".concat(index + 1, \":\"), {\n                                id: doc._id,\n                                title: doc.title,\n                                journeyLength: ((_doc_journey = doc.journey) === null || _doc_journey === void 0 ? void 0 : _doc_journey.length) || 0,\n                                journey: doc.journey\n                            });\n                            if (doc.journey && doc.journey.length > 0) {\n                                var _allEntries;\n                                // Add document title to each journey entry for better context\n                                var journeyWithDocTitle = doc.journey.map(function(entry) {\n                                    return (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_8__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_9__._)({}, entry), {\n                                        documentTitle: doc.title\n                                    });\n                                });\n                                (_allEntries = allEntries).push.apply(_allEntries, (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_10__._)(journeyWithDocTitle));\n                            } else {\n                                console.warn(\"Document \".concat(doc._id, \" has no journey entries\"));\n                            }\n                        });\n                        // Sort entries by timestamp\n                        sortedEntries = allEntries.sort(function(a, b) {\n                            var dateA = new Date(a.timestamp);\n                            var dateB = new Date(b.timestamp);\n                            return dateA.getTime() - dateB.getTime();\n                        });\n                        console.log(\"Combined journey entries:\", sortedEntries.length);\n                        console.log(\"Final journey data:\", JSON.stringify(sortedEntries, null, 2));\n                        setJourney(sortedEntries);\n                        return [\n                            3,\n                            8\n                        ];\n                    case 6:\n                        console.error(\"Invalid document chain data:\", data);\n                        // Fallback to the original journey API if document-chain fails\n                        console.log(\"Falling back to journey API\");\n                        return [\n                            4,\n                            fetchFallbackJourneyData()\n                        ];\n                    case 7:\n                        _state.sent();\n                        _state.label = 8;\n                    case 8:\n                        return [\n                            3,\n                            12\n                        ];\n                    case 9:\n                        error = _state.sent();\n                        console.error(\"Error fetching document chain:\", error);\n                        // Fallback to the original journey API if document-chain fails\n                        console.log(\"Falling back to journey API due to error\");\n                        return [\n                            4,\n                            fetchFallbackJourneyData()\n                        ];\n                    case 10:\n                        _state.sent();\n                        return [\n                            3,\n                            12\n                        ];\n                    case 11:\n                        setLoading(false);\n                        return [\n                            7\n                        ];\n                    case 12:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function fetchJourneyData() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    // Fallback to the original journey API if document-chain fails\n    var fetchFallbackJourneyData = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_6__._)(function() {\n            var encodedDocumentId, apiUrl, response, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_7__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        _state.trys.push([\n                            0,\n                            3,\n                            ,\n                            4\n                        ]);\n                        encodedDocumentId = encodeURIComponent(documentId);\n                        apiUrl = (0,_utils_apiUtils__WEBPACK_IMPORTED_MODULE_4__.getApiUrl)(\"/api/documents/\".concat(encodedDocumentId, \"/journey\"));\n                        console.log(\"Fetching fallback journey data from:\", apiUrl);\n                        return [\n                            4,\n                            fetch(apiUrl, {\n                                method: \"GET\",\n                                headers: _utils_apiUtils__WEBPACK_IMPORTED_MODULE_4__.defaultHeaders\n                            })\n                        ];\n                    case 1:\n                        response = _state.sent();\n                        if (!response.ok) {\n                            console.error(\"Failed to fetch fallback journey data: \".concat(response.status, \" \").concat(response.statusText));\n                            setJourney([]);\n                            return [\n                                2\n                            ];\n                        }\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 2:\n                        data = _state.sent();\n                        if (Array.isArray(data)) {\n                            console.log(\"Fallback journey data received:\", data.length);\n                            console.log(\"Fallback journey data:\", JSON.stringify(data, null, 2));\n                            setJourney(data);\n                        } else {\n                            console.error(\"Fallback journey data is not an array:\", data);\n                            console.log(\"Fallback response data:\", JSON.stringify(data, null, 2));\n                            setJourney([]);\n                        }\n                        return [\n                            3,\n                            4\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error fetching fallback journey data:\", error);\n                        setJourney([]);\n                        return [\n                            3,\n                            4\n                        ];\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function fetchFallbackJourneyData() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\app\\\\disposition-form-preview\\\\page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-700 dark:text-gray-300\",\n                        children: \"Loading disposition form...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\app\\\\disposition-form-preview\\\\page.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\app\\\\disposition-form-preview\\\\page.tsx\",\n                lineNumber: 220,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\app\\\\disposition-form-preview\\\\page.tsx\",\n            lineNumber: 219,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DispositionFormPreview__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        documentId: documentId,\n        documentTitle: documentTitle,\n        journey: journey,\n        isBlank: isBlank\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\app\\\\disposition-form-preview\\\\page.tsx\",\n        lineNumber: 229,\n        columnNumber: 5\n    }, this);\n}\n_s(DispositionFormPreviewPage, \"FqFEstiTYuVjH9TFK/IoOwhbvqA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = DispositionFormPreviewPage;\nvar _c;\n$RefreshReg$(_c, \"DispositionFormPreviewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/disposition-form-preview/page.tsx\n"));

/***/ })

});