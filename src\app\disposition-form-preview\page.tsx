'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import DispositionFormPreview from '@/components/DispositionFormPreview';
import { IDocument, IDocumentJourney } from '@/types';
import { getApiUrl, defaultHeaders } from '@/utils/apiUtils';

export default function DispositionFormPreviewPage() {
  const searchParams = useSearchParams();
  const documentId = searchParams.get('id') || '';
  const documentTitle = searchParams.get('title') || '';
  const isBlank = searchParams.get('blank') === 'true';
  const journeyDataParam = searchParams.get('journeyData');
  const trackingNumberParam = searchParams.get('trackingNumber');

  const [journey, setJourney] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [trackingNumber, setTrackingNumber] = useState<string>('');

  // Debug logging for URL parameters
  useEffect(() => {
    console.log('Disposition Form Preview loaded with params:', {
      documentId,
      documentTitle,
      isBlank,
      hasJourneyData: !!journeyDataParam,
      journeyDataLength: journeyDataParam?.length || 0,
      trackingNumberParam,
      fullURL: window.location.href
    });
  }, [documentId, documentTitle, isBlank, journeyDataParam, trackingNumberParam]);

  useEffect(() => {
    // Check if tracking number is provided in URL parameters
    if (trackingNumberParam) {
      console.log('Using tracking number from URL parameters:', trackingNumberParam);
      setTrackingNumber(trackingNumberParam);
    }

    // Always fetch document data to get tracking number (unless blank or already provided)
    if (!isBlank && documentId && !trackingNumberParam) {
      fetchDocumentData();
    }

    // Check if journey data is provided in URL parameters
    if (journeyDataParam) {
      try {
        const parsedJourney = JSON.parse(decodeURIComponent(journeyDataParam));
        console.log('Using journey data from URL parameters:', parsedJourney.length, 'entries');
        setJourney(parsedJourney);
        if (isBlank || trackingNumberParam) setLoading(false); // If blank or tracking number provided, we're done
        return;
      } catch (error) {
        console.error('Error parsing journey data from URL:', error);
        // Fall through to API fetch
      }
    }

    // Only fetch journey data if not a blank form and no journey data provided
    if (!isBlank && documentId && !journeyDataParam) {
      fetchJourneyData();
    } else if (isBlank) {
      setLoading(false);
    }
  }, [documentId, isBlank, journeyDataParam, trackingNumberParam]);

  // Fetch document data to get the tracking number
  const fetchDocumentData = async () => {
    try {
      const encodedDocumentId = encodeURIComponent(documentId);
      const apiUrl = getApiUrl(`/api/documents/${encodedDocumentId}`);
      console.log('Fetching document data from:', apiUrl);

      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: defaultHeaders
      });

      if (!response.ok) {
        console.error(`Failed to fetch document data: ${response.status} ${response.statusText}`);
        return;
      }

      const data = await response.json();
      console.log('Document data received:', data);

      if (data && data.trackingNumber) {
        console.log('Setting tracking number:', data.trackingNumber);
        setTrackingNumber(data.trackingNumber);
      } else {
        console.warn('No tracking number found in document data');
      }
    } catch (error) {
      console.error('Error fetching document data:', error);
    } finally {
      // Don't set loading to false here, wait for journey data
      if (journeyDataParam || isBlank) {
        setLoading(false);
      }
    }
  };

  const fetchJourneyData = async () => {
    try {
      // Use the document-chain API to get the complete journey
      const encodedDocumentId = encodeURIComponent(documentId);
      const apiUrl = getApiUrl(`/api/documents/${encodedDocumentId}/document-chain`);
      console.log('Fetching complete document chain from:', apiUrl);

      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: defaultHeaders
      });

      if (!response.ok) {
        console.error(`Failed to fetch document chain: ${response.status} ${response.statusText}`);
        setJourney([]);
        return;
      }

      // Parse the response with better error handling
      let data;
      try {
        const text = await response.text();
        console.log('Raw document chain response:', text.substring(0, 100) + (text.length > 100 ? '...' : ''));
        data = JSON.parse(text);
      } catch (parseError) {
        console.error('Error parsing document chain data:', parseError);
        setJourney([]);
        return;
      }

      if (data.success && data.documentChain && Array.isArray(data.documentChain)) {
        console.log('Document chain received:', data.documentChain.length, 'documents');
        console.log('Document chain data:', JSON.stringify(data.documentChain, null, 2));

        // Combine all journey entries from all documents in the chain
        const allEntries: IDocumentJourney[] = [];

        data.documentChain.forEach((doc: IDocument, index: number) => {
          console.log(`Processing document ${index + 1}:`, {
            id: doc._id,
            title: doc.title,
            journeyLength: doc.journey?.length || 0,
            journey: doc.journey
          });

          if (doc.journey && doc.journey.length > 0) {
            // Add document title to each journey entry for better context
            const journeyWithDocTitle = doc.journey.map((entry: IDocumentJourney) => ({
              ...entry,
              documentTitle: doc.title
            }));

            allEntries.push(...journeyWithDocTitle);
          } else {
            console.warn(`Document ${doc._id} has no journey entries`);
          }
        });

        // Sort entries by timestamp
        const sortedEntries = allEntries.sort((a: IDocumentJourney, b: IDocumentJourney) => {
          const dateA = new Date(a.timestamp);
          const dateB = new Date(b.timestamp);
          return dateA.getTime() - dateB.getTime();
        });

        console.log('Combined journey entries:', sortedEntries.length);
        console.log('Final journey data:', JSON.stringify(sortedEntries, null, 2));
        setJourney(sortedEntries);
      } else {
        console.error('Invalid document chain data:', data);

        // Fallback to the original journey API if document-chain fails
        console.log('Falling back to journey API');
        await fetchFallbackJourneyData();
      }
    } catch (error) {
      console.error('Error fetching document chain:', error);

      // Fallback to the original journey API if document-chain fails
      console.log('Falling back to journey API due to error');
      await fetchFallbackJourneyData();
    } finally {
      setLoading(false);
    }
  };

  // Fallback to the original journey API if document-chain fails
  const fetchFallbackJourneyData = async () => {
    try {
      const encodedDocumentId = encodeURIComponent(documentId);
      const apiUrl = getApiUrl(`/api/documents/${encodedDocumentId}/journey`);
      console.log('Fetching fallback journey data from:', apiUrl);

      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: defaultHeaders
      });

      if (!response.ok) {
        console.error(`Failed to fetch fallback journey data: ${response.status} ${response.statusText}`);
        setJourney([]);
        return;
      }

      const data = await response.json();

      if (Array.isArray(data)) {
        console.log('Fallback journey data received:', data.length);
        console.log('Fallback journey data:', JSON.stringify(data, null, 2));
        setJourney(data);
      } else {
        console.error('Fallback journey data is not an array:', data);
        console.log('Fallback response data:', JSON.stringify(data, null, 2));
        setJourney([]);
      }
    } catch (error) {
      console.error('Error fetching fallback journey data:', error);
      setJourney([]);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-700 dark:text-gray-300">Loading disposition form...</p>
        </div>
      </div>
    );
  }

  return (
    <DispositionFormPreview
      documentId={documentId}
      documentTitle={documentTitle}
      journey={journey}
      isBlank={isBlank}
      trackingNumber={trackingNumber}
    />
  );
}
