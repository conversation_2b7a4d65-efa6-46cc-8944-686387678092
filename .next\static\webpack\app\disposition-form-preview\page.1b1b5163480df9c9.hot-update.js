"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/disposition-form-preview/page",{

/***/ "(app-pages-browser)/./src/components/DispositionForm.tsx":
/*!********************************************!*\
  !*** ./src/components/DispositionForm.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DispositionForm; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _utils_qrCodeUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/qrCodeUtils */ \"(app-pages-browser)/./src/utils/qrCodeUtils.ts\");\n/* harmony import */ var _utils_documentHelpers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/documentHelpers */ \"(app-pages-browser)/./src/utils/documentHelpers.ts\");\n/* harmony import */ var _utils_journeyUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/journeyUtils */ \"(app-pages-browser)/./src/utils/journeyUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n// Import QR code components with error handling\n\n\n// Dynamically import QRCode with error handling\nvar QRCode = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(function() {\n    return __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_react-qrcode-logo_dist_index_js\").then(__webpack_require__.t.bind(__webpack_require__, /*! react-qrcode-logo */ \"(app-pages-browser)/./node_modules/react-qrcode-logo/dist/index.js\", 23)).then(function(mod) {\n        return mod.QRCode;\n    });\n}, {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\DispositionForm.tsx -> \" + \"react-qrcode-logo\"\n        ]\n    },\n    ssr: false,\n    loading: function() {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-[80px] h-[80px] bg-white flex items-center justify-center border border-gray-300\",\n            children: \"QR\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n            lineNumber: 15,\n            columnNumber: 20\n        }, _this);\n    }\n});\n_c = QRCode;\n\n\nfunction DispositionForm(param) {\n    var _this = this;\n    var documentId = param.documentId, documentTitle = param.documentTitle, journey = param.journey, _param_isBlank = param.isBlank, isBlank = _param_isBlank === void 0 ? false : _param_isBlank, trackingNumber = param.trackingNumber;\n    _s();\n    // Deduplicate journey entries\n    var deduplicatedJourney = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function() {\n        if (isBlank || !journey || journey.length === 0) return [];\n        console.log(\"Deduplicating \".concat(journey.length, \" journey entries for disposition form\"));\n        var deduplicated = (0,_utils_journeyUtils__WEBPACK_IMPORTED_MODULE_6__.deduplicateJourneyEntries)(journey);\n        console.log(\"After deduplication: \".concat(deduplicated.length, \" entries\"));\n        return deduplicated;\n    }, [\n        journey,\n        isBlank\n    ]);\n    // Get current date in format: Month DD, YYYY\n    var currentDate = new Date().toLocaleDateString(\"en-US\", {\n        month: \"long\",\n        day: \"2-digit\",\n        year: \"numeric\"\n    });\n    // Use the passed tracking number, or try to find one in journey, or generate formatted document ID as fallback\n    var formattedDocumentId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function() {\n        // If a tracking number is explicitly passed, use it\n        if (trackingNumber && trackingNumber !== \"undefined\" && trackingNumber !== \"null\") {\n            return trackingNumber;\n        }\n        // If blank form, return null\n        if (isBlank) return null;\n        // Try to find a document with tracking number in the journey\n        if (journey && journey.length > 0) {\n            var journeyWithTrackingNumber = journey.find(function(j) {\n                return typeof j === \"object\" && j !== null && j.trackingNumber;\n            });\n            if (journeyWithTrackingNumber && journeyWithTrackingNumber.trackingNumber) {\n                var journeyTrackingNumber = journeyWithTrackingNumber.trackingNumber;\n                if (journeyTrackingNumber !== \"undefined\" && journeyTrackingNumber !== \"null\") {\n                    return journeyTrackingNumber;\n                }\n            }\n        }\n        // Fallback to old format (this should rarely be used now)\n        return (0,_utils_documentHelpers__WEBPACK_IMPORTED_MODULE_5__.generateFormattedDocumentId)(documentId, new Date());\n    }, [\n        trackingNumber,\n        journey,\n        isBlank,\n        documentId\n    ]);\n    // Format user name and division for display\n    var formatUser = function(user) {\n        if (typeof user === \"object\" && user !== null) {\n            if (user.name) {\n                // Return name with division if available\n                return user.division ? \"\".concat(user.name, \" (\").concat(user.division, \")\") : user.name;\n            }\n        }\n        return \"Unknown User\";\n    };\n    return(// IMPORTANT: This component has its own border - do NOT add another border in parent components\n    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"dispositionForm\",\n        className: \"bg-white text-black p-6 mx-auto\",\n        style: {\n            width: \"8in\",\n            height: \"11in\",\n            boxSizing: \"border-box\",\n            color: \"#000000\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            border: \"1px solid #000000\" /* This is the ONLY border that should be applied to this form */ \n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-[90px] h-[90px] relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: \"/MGB Logo_Proxy.png\",\n                            alt: \"MGB Logo\",\n                            width: 90,\n                            height: 90,\n                            style: {\n                                objectFit: \"contain\"\n                            },\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-grow text-center mx-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs font-medium\",\n                                style: {\n                                    color: \"#000000\"\n                                },\n                                children: \"Republic of the Philippines\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs font-medium\",\n                                style: {\n                                    color: \"#000000\"\n                                },\n                                children: \"Department of Environment and Natural Resources\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-bold tracking-wide\",\n                                style: {\n                                    color: \"#0000FF\"\n                                },\n                                children: \"MINES AND GEOSCIENCES BUREAU\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-bold tracking-wide\",\n                                style: {\n                                    color: \"#0000FF\"\n                                },\n                                children: \"Regional Office No. II\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-[10px]\",\n                                style: {\n                                    color: \"#000000\"\n                                },\n                                children: \"No. 18 Dalan na Pagayaya Corner Matuwung, Regional Government Center\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-[10px]\",\n                                style: {\n                                    color: \"#000000\"\n                                },\n                                children: \"Carig Sur, Tuguegarao City\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-[10px]\",\n                                style: {\n                                    color: \"#000000\"\n                                },\n                                children: \"Telefax: (+63 078)304-5551, (+63 078)304-0508, (+63 078)304-0694\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-[10px]\",\n                                style: {\n                                    color: \"#000000\"\n                                },\n                                children: [\n                                    \"E-mail: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            color: \"#000000\"\n                                        },\n                                        children: \"<EMAIL>\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 75\n                                    }, this),\n                                    \" | \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            color: \"#000000\"\n                                        },\n                                        children: \"<EMAIL>\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 138\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-[90px] h-[90px] relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: \"/Bagong_Pilipinas_logo.png\",\n                            alt: \"Bagong Pilipinas Logo\",\n                            width: 90,\n                            height: 90,\n                            style: {\n                                objectFit: \"contain\"\n                            },\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t-2 border-yellow-500 w-full mb-2\",\n                style: {\n                    borderTopWidth: \"2px\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-lg font-bold uppercase tracking-wider\",\n                    style: {\n                        color: \"#000000\"\n                    },\n                    children: \"DISPOSITION FORM\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex mb-3 justify-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-grow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: \"#000000\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"font-semibold\",\n                                                        style: {\n                                                            color: \"#000000\"\n                                                        },\n                                                        children: \"Doc. Date:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 47\n                                                    }, this),\n                                                    \" \",\n                                                    currentDate\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: \"#000000\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"font-semibold\",\n                                                        style: {\n                                                            color: \"#000000\"\n                                                        },\n                                                        children: \"DTN:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 47\n                                                    }, this),\n                                                    \" \",\n                                                    formattedDocumentId\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: \"#000000\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    className: \"font-semibold\",\n                                                    style: {\n                                                        color: \"#000000\"\n                                                    },\n                                                    children: \"Company Name:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 47\n                                                }, this),\n                                                \" MINES AND GEOSCIENCES BUREAU - REGION II (CAGAYAN VALLEY)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 text-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: \"#000000\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            className: \"font-semibold\",\n                                            style: {\n                                                color: \"#000000\"\n                                            },\n                                            children: \"Subject / Title:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 45\n                                        }, this),\n                                        \" \",\n                                        documentTitle\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white -mt-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-[80px] h-[80px] bg-white flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Suspense), {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-[80px] h-[80px] bg-white flex items-center justify-center border border-gray-300 text-xs\",\n                                        children: \"QR Code\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 41\n                                    }, void 0),\n                                    children: !isBlank ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QRCode, (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({\n                                        value: (0,_utils_qrCodeUtils__WEBPACK_IMPORTED_MODULE_4__.generateDocumentQrContent)(documentId, documentTitle)\n                                    }, _utils_qrCodeUtils__WEBPACK_IMPORTED_MODULE_4__.dispositionFormQrConfig), {\n                                        size: 80\n                                    }), void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QRCode, (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({\n                                        value:  true ? \"\".concat(window.location.origin, \"/blank-form\") : 0\n                                    }, _utils_qrCodeUtils__WEBPACK_IMPORTED_MODULE_4__.dispositionFormQrConfig), {\n                                        size: 80\n                                    }), void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t-2 border-yellow-500 w-full mb-2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center font-bold mb-2 text-base tracking-wide\",\n                        style: {\n                            color: \"#000000\"\n                        },\n                        children: \"ROUTED\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"w-full border-collapse border border-black mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"bg-gray-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"border border-black p-1 text-center w-1/5 text-xs font-bold\",\n                                            style: {\n                                                color: \"#000000\"\n                                            },\n                                            children: [\n                                                \"BY\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"(Office Code Name/\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 42\n                                                }, this),\n                                                \"Office Code Initial)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"border border-black p-1 text-center w-1/5 text-xs font-bold\",\n                                            style: {\n                                                color: \"#000000\"\n                                            },\n                                            children: [\n                                                \"DATE\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"(mm-dd-yy)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"border border-black p-1 text-center w-1/5 text-xs font-bold\",\n                                            style: {\n                                                color: \"#000000\"\n                                            },\n                                            children: [\n                                                \"TO\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"(Office Code Name/\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 42\n                                                }, this),\n                                                \"Office Code Initial)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"border border-black p-1 text-center w-1/5 text-xs font-bold\",\n                                            style: {\n                                                color: \"#000000\"\n                                            },\n                                            children: [\n                                                \"TIME\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"(AM/PM)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"border border-black p-1 text-center w-1/5 text-xs font-bold\",\n                                            style: {\n                                                color: \"#000000\"\n                                            },\n                                            children: \"ACTION / REMARKS / STATUS\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                children: !isBlank && Array.isArray(journey) && journey.length > 0 ? // Render journey rows and add empty rows to make it exactly 15 rows\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        deduplicatedJourney.filter(function(event) {\n                                            return [\n                                                \"FORWARDED\",\n                                                \"RECEIVED\",\n                                                \"SENT\",\n                                                \"CREATED_AND_SENT\",\n                                                \"FORWARDED_RECEIVED\"\n                                            ].includes(event.action);\n                                        }).map(function(event, index) {\n                                            var eventDate = new Date(event.timestamp);\n                                            var formattedDate = \"\".concat((eventDate.getMonth() + 1).toString().padStart(2, \"0\"), \"-\").concat(eventDate.getDate().toString().padStart(2, \"0\"), \"-\").concat(eventDate.getFullYear().toString().slice(2));\n                                            var formattedTime = eventDate.toLocaleTimeString(\"en-US\", {\n                                                hour: \"2-digit\",\n                                                minute: \"2-digit\",\n                                                hour12: true\n                                            });\n                                            // We'll check for documentTitle property later\n                                            // Determine action text based on event action and notes\n                                            var actionText = \"\";\n                                            // Check if the event has specific notes that should be displayed\n                                            if (event.notes) {\n                                                // If notes start with a letter code (A-Z), keep as is\n                                                if (/^[A-Z] - /.test(event.notes)) {\n                                                    actionText = event.notes;\n                                                } else {\n                                                    // Otherwise, prefix with \"A - \" for consistency\n                                                    actionText = \"A - \".concat(event.notes);\n                                                }\n                                            } else if (event.action === \"CREATED_AND_SENT\") {\n                                                actionText = \"A - Document created and routed\";\n                                            } else if (event.action === \"SENT\") {\n                                                actionText = \"A - Document sent\";\n                                            } else if (event.action === \"FORWARDED\") {\n                                                actionText = \"C - Please take appropriate action\";\n                                            } else if (event.action === \"FORWARDED_RECEIVED\") {\n                                                actionText = \"C - Document forwarded to new recipient\";\n                                            } else if (event.action === \"RECEIVED\") {\n                                                actionText = \"D - For review and action\";\n                                            } else if (event.action === \"PENDING\") {\n                                                actionText = \"B - Awaiting further instructions\";\n                                            }\n                                            // If this event has a document title and it's different from the main document title,\n                                            // add it to the action text to provide context\n                                            var eventDocTitle = event.documentTitle;\n                                            if (eventDocTitle && eventDocTitle.trim() !== \"\") {\n                                                // Check if the document title from the journey is different from the main document title\n                                                if (eventDocTitle !== documentTitle) {\n                                                    actionText += \" (Doc: \".concat(eventDocTitle, \")\");\n                                                }\n                                            }\n                                            // Get the next event in the journey for consistent tracking\n                                            var nextEvent = index < journey.length - 1 ? journey[index + 1] : null;\n                                            // Determine recipient information\n                                            var recipientInfo = \"\";\n                                            // Use recipientInfo if available\n                                            if (event.recipientInfo) {\n                                                var recipient = event.recipientInfo;\n                                                var name = recipient.name || \"Unknown\";\n                                                var division = recipient.division || \"\";\n                                                recipientInfo = division ? \"\".concat(name, \" (\").concat(division, \")\") : name;\n                                            } else if (event.toDivision === \"ORD\") {\n                                                // Find a user with ORD division in the journey\n                                                var ordUser = journey.find(function(j) {\n                                                    return typeof j.byUser === \"object\" && j.byUser !== null && j.byUser.division === \"ORD\";\n                                                });\n                                                if (ordUser && typeof ordUser.byUser === \"object\" && ordUser.byUser !== null) {\n                                                    var name1 = ordUser.byUser.name || \"Unknown\";\n                                                    recipientInfo = \"\".concat(name1, \" (ORD)\");\n                                                } else {\n                                                    recipientInfo = \"ORD\"; // Fallback to just division if no user found\n                                                }\n                                            } else if ((event.action === \"CREATED_AND_SENT\" || event.action === \"FORWARDED\") && nextEvent && typeof nextEvent.byUser === \"object\" && nextEvent.byUser !== null) {\n                                                var nextUser = nextEvent.byUser;\n                                                var name2 = nextUser.name || \"Unknown\";\n                                                var division1 = nextUser.division || \"\";\n                                                recipientInfo = division1 ? \"\".concat(name2, \" (\").concat(division1, \")\") : name2;\n                                            } else if (event.toDivision) {\n                                                // Try to find a user with this division in the journey\n                                                var divisionUser = journey.find(function(j) {\n                                                    return typeof j.byUser === \"object\" && j.byUser !== null && j.byUser.division === event.toDivision;\n                                                });\n                                                if (divisionUser && typeof divisionUser.byUser === \"object\" && divisionUser.byUser !== null) {\n                                                    var name3 = divisionUser.byUser.name || \"Unknown\";\n                                                    recipientInfo = \"\".concat(name3, \" (\").concat(event.toDivision, \")\");\n                                                } else {\n                                                    recipientInfo = event.toDivision;\n                                                }\n                                            } else if (event.action === \"RECEIVED\" && typeof event.byUser === \"object\" && event.byUser !== null) {\n                                                recipientInfo = formatUser(event.byUser);\n                                            }\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: index % 2 === 0 ? \"bg-white\" : \"bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"border border-black p-1 text-center h-6 text-xs font-medium\",\n                                                        style: {\n                                                            color: \"#000000\"\n                                                        },\n                                                        children: typeof event.byUser === \"object\" && event.byUser !== null ? formatUser(event.byUser) : \"Unknown\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 23\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"border border-black p-1 text-center h-6 text-xs font-medium\",\n                                                        style: {\n                                                            color: \"#000000\"\n                                                        },\n                                                        children: formattedDate\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 23\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"border border-black p-1 text-center h-6 text-xs font-medium\",\n                                                        style: {\n                                                            color: \"#000000\"\n                                                        },\n                                                        children: recipientInfo\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 23\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"border border-black p-1 text-center h-6 text-xs font-medium\",\n                                                        style: {\n                                                            color: \"#000000\"\n                                                        },\n                                                        children: formattedTime\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 23\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"border border-black p-1 text-center h-6 text-xs font-medium\",\n                                                        style: {\n                                                            color: \"#000000\"\n                                                        },\n                                                        children: actionText\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 23\n                                                    }, _this)\n                                                ]\n                                            }, \"journey-\".concat(index), true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 21\n                                            }, _this);\n                                        }),\n                                        function() {\n                                            // Calculate the number of filtered journey entries (only Forward, Receive, and Sent actions)\n                                            var filteredJourneyLength = deduplicatedJourney.filter(function(event) {\n                                                return [\n                                                    \"FORWARDED\",\n                                                    \"RECEIVED\",\n                                                    \"SENT\",\n                                                    \"CREATED_AND_SENT\",\n                                                    \"FORWARDED_RECEIVED\"\n                                                ].includes(event.action);\n                                            }).length;\n                                            // Only add empty rows if we have fewer than 15 rows\n                                            return filteredJourneyLength < 15 ? Array.from({\n                                                length: 15 - filteredJourneyLength\n                                            }).map(function(_, index) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: (filteredJourneyLength + index) % 2 === 0 ? \"bg-white\" : \"bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-black p-1 h-6\",\n                                                            style: {\n                                                                color: \"#000000\"\n                                                            },\n                                                            children: \"\\xa0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 25\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-black p-1 h-6\",\n                                                            style: {\n                                                                color: \"#000000\"\n                                                            },\n                                                            children: \"\\xa0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 25\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-black p-1 h-6\",\n                                                            style: {\n                                                                color: \"#000000\"\n                                                            },\n                                                            children: \"\\xa0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 25\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-black p-1 h-6\",\n                                                            style: {\n                                                                color: \"#000000\"\n                                                            },\n                                                            children: \"\\xa0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 25\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"border border-black p-1 h-6\",\n                                                            style: {\n                                                                color: \"#000000\"\n                                                            },\n                                                            children: \"\\xa0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 25\n                                                        }, _this)\n                                                    ]\n                                                }, \"empty-\".concat(index), true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 23\n                                                }, _this);\n                                            }) : null;\n                                        }()\n                                    ]\n                                }, void 0, true) : // If blank or no journey, add exactly 15 empty rows\n                                Array.from({\n                                    length: 15\n                                }).map(function(_, index) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: index % 2 === 0 ? \"bg-white\" : \"bg-gray-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"border border-black p-1 h-6\",\n                                                style: {\n                                                    color: \"#000000\"\n                                                },\n                                                children: \"\\xa0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"border border-black p-1 h-6\",\n                                                style: {\n                                                    color: \"#000000\"\n                                                },\n                                                children: \"\\xa0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"border border-black p-1 h-6\",\n                                                style: {\n                                                    color: \"#000000\"\n                                                },\n                                                children: \"\\xa0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"border border-black p-1 h-6\",\n                                                style: {\n                                                    color: \"#000000\"\n                                                },\n                                                children: \"\\xa0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"border border-black p-1 h-6\",\n                                                style: {\n                                                    color: \"#000000\"\n                                                },\n                                                children: \"\\xa0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 19\n                                            }, _this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 17\n                                    }, _this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xs mb-2 mx-auto text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-bold mb-1\",\n                        style: {\n                            color: \"#000000\"\n                        },\n                        children: \"Action Codes for Document Routing:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                        lineNumber: 387,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-x-2 gap-y-1 px-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-[9px] font-medium text-left\",\n                                style: {\n                                    color: \"#000000\"\n                                },\n                                children: \"A - For information and reference\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-[9px] font-medium text-left\",\n                                style: {\n                                    color: \"#000000\"\n                                },\n                                children: \"B - For comments and recommendations\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-[9px] font-medium text-left\",\n                                style: {\n                                    color: \"#000000\"\n                                },\n                                children: \"C - Please take appropriate action\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-[9px] font-medium text-left\",\n                                style: {\n                                    color: \"#000000\"\n                                },\n                                children: \"D - For review and action\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-[9px] font-medium text-left\",\n                                style: {\n                                    color: \"#000000\"\n                                },\n                                children: \"E - Please expedite\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-[9px] font-medium text-left\",\n                                style: {\n                                    color: \"#000000\"\n                                },\n                                children: \"F - For immediate investigation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-[9px] font-medium text-left\",\n                                style: {\n                                    color: \"#000000\"\n                                },\n                                children: \"G - Please attach supporting papers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-[9px] font-medium text-left\",\n                                style: {\n                                    color: \"#000000\"\n                                },\n                                children: \"H - For approval\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-[9px] font-medium text-left\",\n                                style: {\n                                    color: \"#000000\"\n                                },\n                                children: \"I - For signature\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-[9px] font-medium text-left\",\n                                style: {\n                                    color: \"#000000\"\n                                },\n                                children: \"J - Please study and evaluate\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-[9px] font-medium text-left\",\n                                style: {\n                                    color: \"#000000\"\n                                },\n                                children: \"K - Please release/file\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-[9px] font-medium text-left\",\n                                style: {\n                                    color: \"#000000\"\n                                },\n                                children: \"L - Update status\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-[9px] font-medium text-left\",\n                                style: {\n                                    color: \"#000000\"\n                                },\n                                children: \"M - Filed/Closed\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-[9px] font-medium text-left\",\n                                style: {\n                                    color: \"#000000\"\n                                },\n                                children: \"N - For check preparation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-[9px] font-medium text-left\",\n                                style: {\n                                    color: \"#000000\"\n                                },\n                                children: \"O - For discussion\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                lineNumber: 386,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t-2 border-yellow-500 w-full mb-2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                lineNumber: 408,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xs italic mt-auto pt-1 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    style: {\n                        color: \"#000000\",\n                        fontStyle: \"italic\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            className: \"font-bold\",\n                            style: {\n                                color: \"#000000\"\n                            },\n                            children: \"Important Reminder:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 62\n                        }, this),\n                        \" For complex/sensitive or separate sheet if necessary. Attach this always with the document to be routed as this shall form an integral part of the document proper.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                    lineNumber: 412,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n                lineNumber: 411,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Euf\\\\Coding\\\\DocumentTracker\\\\src\\\\components\\\\DispositionForm.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this));\n}\n_s(DispositionForm, \"4M7lwoDAHM9IeqzSN49lp7Iu8i4=\");\n_c1 = DispositionForm;\nvar _c, _c1;\n$RefreshReg$(_c, \"QRCode\");\n$RefreshReg$(_c1, \"DispositionForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DispositionForm.tsx\n"));

/***/ })

});