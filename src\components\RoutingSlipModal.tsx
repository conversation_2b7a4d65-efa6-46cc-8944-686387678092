'use client';

import { useState, useEffect, useMemo } from 'react';
import { IDocument, IDocumentJourney } from '@/types';
import { getJourneyIcon, getJourneyColor, formatDate, generateFormattedDocumentId } from '@/utils/documentHelpers';
import { deduplicateJourneyEntries } from '@/utils/journeyUtils';

interface RoutingSlipModalProps {
  isOpen: boolean;
  onClose: () => void;
  documentId: string;
  documentTitle: string;
  journey: (IDocumentJourney & {
    byUser: {
      name?: string;
      division?: string;
      _id?: string;
    } | string;
  })[];
}

// Helper function to format action names for better readability
const formatActionName = (action: string | undefined | null): string => {
  // Handle undefined or null action
  if (!action) {
    console.warn('No action provided for formatting action name');
    return 'Unknown Action';
  }

  try {
    switch (action) {
      case 'CREATED_AND_SENT':
        return 'Created and Sent';
      case 'FORWARDED_RECEIVED':
        return 'Forwarded to New Recipient';
      case 'RECEIVED_FORWARDED':
        return 'Received and Forwarded';
      case 'CREATED':
        return 'Created';
      case 'SENT':
        return 'Sent';
      case 'RECEIVED':
        return 'Received';
      case 'FORWARDED':
        return 'Forwarded';
      case 'PENDING':
        return 'Pending';
      case 'PROCESSED':
        return 'Processed';
      case 'ARCHIVED':
        return 'Archived';
      case 'CREATED_AND_ARCHIVED':
        return 'Created and Archived';
      default:
        // Convert SNAKE_CASE to Title Case
        return action.replace(/_/g, ' ')
          .toLowerCase()
          .split(' ')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
    }
  } catch (error) {
    console.error('Error formatting action name:', error, 'Action:', action);
    return 'Unknown Action';
  }
};

export default function RoutingSlipModal({
  isOpen,
  onClose,
  documentId,
  documentTitle,
  journey
}: RoutingSlipModalProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [documentChain, setDocumentChain] = useState<IDocument[]>([]);
  const [allJourneyEntries, setAllJourneyEntries] = useState<(IDocumentJourney & { documentTitle?: string })[]>([]);
  const [trackingNumber, setTrackingNumber] = useState<string>('');

  // Fetch the document chain when the modal is opened
  useEffect(() => {
    if (isOpen && documentId) {
      fetchDocumentChain();
    }
  }, [isOpen, documentId]);

  // Fetch the document chain from the API
  const fetchDocumentChain = async () => {
    setLoading(true);
    setError(null);

    try {
      // Fetch the document chain
      const response = await fetch(`/api/documents/${documentId}/document-chain`);

      if (!response.ok) {
        throw new Error('Failed to fetch document chain');
      }

      const data = await response.json();

      if (data.success && data.documentChain) {
        console.log('Document chain:', data.documentChain);
        setDocumentChain(data.documentChain);

        // Extract tracking number from the first document in the chain
        if (data.documentChain.length > 0 && data.documentChain[0].trackingNumber) {
          console.log('Setting tracking number from document chain:', data.documentChain[0].trackingNumber);
          setTrackingNumber(data.documentChain[0].trackingNumber);
        }

        // Combine all journey entries from all documents in the chain
        const allEntries: (IDocumentJourney & { documentTitle?: string })[] = [];

        data.documentChain.forEach((doc: IDocument) => {
          if (doc.journey && doc.journey.length > 0) {
            // Add document title to each journey entry
            const journeyWithDocTitle = doc.journey.map(entry => ({
              ...entry,
              documentTitle: doc.title
            }));

            allEntries.push(...journeyWithDocTitle);
          }
        });

        // First deduplicate entries, then sort by timestamp
        console.log(`Deduplicating ${allEntries.length} journey entries`);
        const deduplicatedEntries = deduplicateJourneyEntries(allEntries);

        // Sort entries by timestamp
        const sortedEntries = deduplicatedEntries.sort((a, b) => {
          const dateA = new Date(a.timestamp);
          const dateB = new Date(b.timestamp);
          return dateA.getTime() - dateB.getTime();
        });

        console.log(`After deduplication: ${deduplicatedEntries.length} entries`);
        setAllJourneyEntries(sortedEntries);
      } else {
        throw new Error(data.message || 'Failed to fetch document chain');
      }
    } catch (err: any) {
      console.error('Error fetching document chain:', err);
      setError(err.message || 'An error occurred while fetching the document chain');

      // Fallback to using the provided journey if document chain fetch fails
      console.log(`Falling back to provided journey with ${journey?.length || 0} entries`);
      const deduplicated = deduplicateJourneyEntries(journey);
      console.log(`After deduplication: ${deduplicated.length} entries`);
      setAllJourneyEntries(deduplicated);
    } finally {
      setLoading(false);
    }
  };

  // Handle opening the disposition form preview in a new tab
  const handlePrintDF = () => {
    // Use the fetched journey data if available, otherwise fall back to the provided journey
    const journeyToUse = allJourneyEntries && allJourneyEntries.length > 0 ? allJourneyEntries : journey;

    // Debug logging
    console.log('Print DF clicked with:', {
      documentId,
      documentTitle,
      journeyLength: journey?.length || 0,
      allJourneyEntriesLength: allJourneyEntries?.length || 0,
      journeyToUse: journeyToUse,
      journeyToUseLength: journeyToUse?.length || 0
    });

    // Pass the journey data and tracking number as URL parameters to avoid API calls
    const journeyData = encodeURIComponent(JSON.stringify(journeyToUse || []));
    const trackingNumberParam = trackingNumber ? `&trackingNumber=${encodeURIComponent(trackingNumber)}` : '';
    const url = `/disposition-form-preview?id=${documentId}&title=${encodeURIComponent(documentTitle)}&blank=false&journeyData=${journeyData}${trackingNumberParam}`;
    console.log('Opening Print DF URL:', url);
    window.open(url, '_blank');
  };

  // Handle opening a blank disposition form preview in a new tab
  const handleBlankDF = () => {
    // Open in a new tab
    const url = `/disposition-form-preview?id=${documentId}&title=${encodeURIComponent(documentTitle)}&blank=true`;
    window.open(url, '_blank');
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-hidden flex flex-col" style={{
        backgroundColor: 'rgba(var(--theme-bg-secondary), 1)',
        color: 'rgba(var(--theme-text-primary), 1)'
      }}>
        <div className="px-6 py-4 flex justify-between items-center" style={{
          borderBottom: '1px solid rgba(var(--theme-border-primary), 1)'
        }}>
          <h3 className="text-lg font-medium" style={{ color: 'rgba(var(--theme-text-primary), 1)' }}>Document Routing Slip</h3>
          <button
            onClick={onClose}
            className="focus:outline-none" style={{ color: 'rgba(var(--theme-text-secondary), 1)' }}
          >
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="p-6 overflow-y-auto flex-grow">
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">Document ID</h4>
            <p className="text-sm text-gray-900 dark:text-white">{generateFormattedDocumentId(documentId, new Date())}</p>
          </div>

          <div className="mb-6">
            <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">Title</h4>
            <p className="text-sm text-gray-900 dark:text-white">{documentTitle}</p>
          </div>

          <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-4">Complete Routing History</h4>

          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500 dark:border-primary-400"></div>
            </div>
          ) : error && allJourneyEntries.length === 0 ? (
            <div className="p-4 rounded-lg bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400">
              <p>{error}</p>
            </div>
          ) : allJourneyEntries && allJourneyEntries.length > 0 ? (
            <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
              <ul className="divide-y divide-gray-200 dark:divide-gray-700">
                {allJourneyEntries.map((event, index) => (
                  <li key={index} className="p-4 flex items-start">
                    <div className={`flex-shrink-0 h-10 w-10 rounded-full ${getJourneyColor(event.action)} flex items-center justify-center mr-4`}>
                      {getJourneyIcon(event.action)}
                    </div>
                    <div className="flex-grow">
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {formatActionName(event.action)} by {
                          (() => {
                            try {
                              if (typeof event.byUser === 'object' && event.byUser !== null) {
                                const user = event.byUser as any;

                                // Check if it's a populated user object with name and division
                                if (user.name && user.division) {
                                  return `${user.name} (${user.division})`;
                                }
                                // Check if it's a populated user object with just a name
                                else if (user.name) {
                                  return user.name;
                                }
                                // Check if it's an ObjectId (has toString method but no name)
                                else if (user.toString && typeof user.toString === 'function') {
                                  const userId = user.toString();

                                  // For archived documents, try to extract user info from notes
                                  if (event.action === 'ARCHIVED' && event.notes) {
                                    const match = event.notes.match(/Document archived by ([^(]+) \(([^)]+)\)/);
                                    if (match) {
                                      const [_, name, division] = match;
                                      return `${name} (${division})`;
                                    }
                                  }

                                  return 'User ID: ' + userId.substring(0, 6) + '...';
                                }
                              } else if (typeof event.byUser === 'string') {
                                // For archived documents, try to extract user info from notes
                                if (event.action === 'ARCHIVED' && event.notes) {
                                  const match = event.notes.match(/Document archived by ([^(]+) \(([^)]+)\)/);
                                  if (match) {
                                    const [_, name, division] = match;
                                    return `${name} (${division})`;
                                  }
                                }

                                // Handle string user IDs
                                return 'User ID: ' + event.byUser.substring(0, 6) + '...';
                              }
                              return 'Unknown User';
                            } catch (error) {
                              console.error('Error formatting user info:', error, 'Event:', event);
                              return 'Unknown User';
                            }
                          })()
                        }
                      </p>

                      {/* Show document title */}
                      {event.documentTitle && (
                        <p className="text-xs font-medium text-blue-600 dark:text-blue-400 mt-1">
                          Document: {event.documentTitle}
                        </p>
                      )}

                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {formatDate(event.timestamp?.toString() || '')}
                      </p>
                      {event.notes && (
                        <p className="mt-1 text-sm text-gray-600 dark:text-gray-300">
                          {event.notes}
                        </p>
                      )}
                      {event.fromDivision && event.toDivision && (
                        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                          From {event.fromDivision} to {event.toDivision}
                        </p>
                      )}
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-300 dark:text-gray-600 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
              <p>No routing history available</p>
            </div>
          )}

          {/* Show a note about the complete journey */}
          {allJourneyEntries && allJourneyEntries.length > 0 && (
            <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 text-blue-700 dark:text-blue-400 rounded-lg text-sm">
              <p className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                This routing slip shows the complete journey of the document, including all forwarded copies.
              </p>
            </div>
          )}
        </div>

        <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-between">
          <div className="space-x-2">
            <button
              onClick={handlePrintDF}
              className="btn btn-primary"
              title="Generate a disposition form with the current routing history"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
              </svg>
              Print DF
            </button>
            <button
              onClick={handleBlankDF}
              className="btn btn-outline"
              title="Generate a blank disposition form"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Blank DF
            </button>
          </div>
          <button
            onClick={onClose}
            className="btn btn-outline"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
}
